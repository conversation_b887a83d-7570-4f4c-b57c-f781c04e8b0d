<?php
/* Copyright (C) 2024 GTB Aging Reports Module
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 */

include_once DOL_DOCUMENT_ROOT.'/core/modules/DolibarrModules.class.php';

/**
 * Description and activation class for module gtbcusage
 */
class modgtbcusage extends DolibarrModules
{
    /**
     * Constructor. Define names, constants, directories, boxes, permissions
     *
     * @param DoliDB $db Database handler
     */
    public function __construct($db)
    {
        global $langs, $conf;

        $this->db = $db;

        // Id for module (must be unique).
        // Use here a free id (See in Home -> System information -> Dolibarr for list of used modules id).
        $this->numero = 500111;

        // Key text used to identify module (for permissions, menus, etc...)
        $this->rights_class = 'gtbcusage';

        // Family can be 'base' (core modules),'crm','financial','hr','projects','products','ecm','technic' (transverse modules),'interface' (interface modules),'other','...'
        // It is used to group modules by family in module setup page
        $this->family = "financial";

        // Module position in the family on 2 digits ('01', '10', '20', ...)
        $this->module_position = '90';

        // Gives the possibility for the module, to provide his own family info and position of this family (Overwrite $this->family and $this->module_position. Avoid this)
        //$this->familyinfo = array('myownfamily' => array('position' => '01', 'label' => $langs->trans("MyOwnFamily")));
        // Module label (no space allowed), used if translation string 'ModulegtbcusageName' not found (gtbcusage is name of module).
        $this->name = preg_replace('/^mod/i', '', get_class($this));

        // Module description, used if translation string 'ModulegtbcusageDesc' not found (gtbcusage is name of module).
        $this->description = "Aging Reports for Customers and Suppliers";

        // Used only if file README.md and README-LL.md not found.
        $this->descriptionlong = "This module provides aging reports to track outstanding balances for customers and suppliers.";

        // Author
        $this->editor_name = 'GTB Solutions';
        $this->editor_url = '';

        // Possible values for version are: 'development', 'experimental', 'dolibarr', 'dolibarr_deprecated' or a version string like 'x.y.z'
        $this->version = '6.09';

        // Url to the file with your last numberversion of this module
        //$this->url_last_version = '';

        // Key used in llx_const table to save module status enabled/disabled (where gtbcusage is value of property name of module in uppercase)
        $this->const_name = 'MAIN_MODULE_'.strtoupper($this->name);

        // Name of image file used for this module.
        // If file is in theme/yourtheme/img directory under name object_pictovalue.png, use this->picto='pictovalue'
        // If file is in module/img directory under name object_pictovalue.png, use this->picto='pictovalue@module'
        // To use a supported fa-xxx css style of font awesome, use this->picto='xxx'
        $this->picto = 'bill';

        // Define some features supported by module (triggers, login, substitutions, menus, css, etc...)
        $this->module_parts = array(
            // Set this to 1 if module has its own trigger directory (core/triggers)
            'triggers' => 0,
            // Set this to 1 if module has its own login method file (core/login)
            'login' => 0,
            // Set this to 1 if module has its own substitution function file (core/substitutions)
            'substitutions' => 0,
            // Set this to 1 if module has its own menus handler directory (core/menus)
            'menus' => 0,
            // Set this to 1 if module overwrite template dir (core/tpl)
            'tpl' => 0,
            // Set this to 1 if module has its own barcode directory (core/modules/barcode)
            'barcode' => 0,
            // Set this to 1 if module has its own models directory (core/modules/xxx)
            'models' => 0,
            // Set this to 1 if module has its own printing directory (core/modules/printing)
            'printing' => 0,
            // Set this to 1 if module has its own theme directory (theme)
            'theme' => 0,
            // Set this to relative path of css file if module has its own css file
            'css' => array(),
            // Set this to relative path of js file if module has its own js file
            'js' => array(),
            // Set here all hooks context managed by module. To find available hook context, make a "grep -r '>initHooks(' *" on source code. You can also set hook context to 'all'
            'hooks' => array(),
            // Set here all workflow context managed by module. You can also set workflow context to 'all'
            'workflow' => array()
        );

        // Data directories to create when module is enabled.
        // Example: this->dirs = array("/gtbcusage/temp","/gtbcusage/subdir");
        $this->dirs = array("/gtbcusage");

        // Config pages. Put here list of php page, stored into gtbcusage/admin directory, to use to setup module.
        $this->config_page_url = array();

        // Dependencies
        // A condition to hide module
        $this->hidden = false;
        // List of module class names as string that must be enabled if this module is enabled. Example: array('always1'=>'modModuleToEnable1','always2'=>'modModuleToEnable2', 'FR1'=>'modModuleToEnableFR'...)
        $this->depends = array();
        $this->requiredby = array(); // List of module class names as string to disable if this one is disabled. Example: array('modModuleToDisable1', ...)
        $this->conflictwith = array(); // List of module class names as string this module is in conflict with. Example: array('modModuleToDisable1', ...)

        // The language file dedicated to your module
        $this->langfiles = array("gtbcusage@gtbcusage");

        // Prerequisites
        $this->phpmin = array(7, 0); // Minimum version of PHP required by module
        $this->need_dolibarr_version = array(11, -3); // Minimum version of Dolibarr required by module

        // Messages at activation
        $this->warnings_activation = array(); // Warning to show when we activate module. array('always'='text') or array('FR'='textfr','MX'='textmx'...)
        $this->warnings_activation_ext = array(); // Warning to show when we activate an external module. array('always'='text') or array('FR'='textfr','MX'='textmx'...)
        //$this->automatic_activation = array('FR'=>'gtbcusageWasAutomaticallyActivatedBecauseOfYourCountryChoice');
        //$this->always_enabled = true; // If true, can't be disabled

        // Constants
        // List of particular constants to add when module is enabled (key, 'chaine', value, desc, visible, 'current' or 'allentities', deleteonunactive)
        // Example: $this->const=array(1 => array('gtbcusage_MYNEWCONST1', 'chaine', 'myvalue', 'This is a constant to add', 1),
        //                             2 => array('gtbcusage_MYNEWCONST2', 'chaine', 'myvalue', 'This is another constant to add', 0, 'current', 1)
        // );
        $this->const = array();

        // Array to add new pages in new tabs
        $this->tabs = array();

        // Dictionaries
        $this->dictionaries = array();

        // Boxes/Widgets
        // Add here list of php file(s) stored in gtbcusage/core/boxes that contains a class to show a widget.
        $this->boxes = array();

        // Cronjobs (List of cron jobs entries to add when module is enabled)
        // unit_frequency must be 60 for minute, 3600 for hour, 86400 for day, 604800 for week
        $this->cronjobs = array();

        // Permissions provided by this module
        $this->rights = array();
        $r = 0;
        // Add here entries to declare new permissions
        // Example:
        $this->rights[$r][0] = $this->numero . sprintf("%02d", $r + 1); // Permission id (must not be already used)
        $this->rights[$r][1] = 'Read gtbcusage reports'; // Permission label
        $this->rights[$r][2] = 'r'; // Permission by default for new user (0/1)
        $this->rights[$r][3] = 0; // Permission by default for new user (0/1)
        $this->rights[$r][4] = 'canuse'; // In php code, permission will be checked by test if ($user->rights->gtbcusage->level1->level2)
        $this->rights[$r][5] = ''; // In php code, permission will be checked by test if ($user->rights->gtbcusage->level1->level2)
        $r++;
        $this->rights[$r][0] = $this->numero . sprintf("%02d", $r + 1);
        $this->rights[$r][1] = 'Create/Update gtbcusage reports';
        $this->rights[$r][2] = 'w';
        $this->rights[$r][3] = 0;
        $this->rights[$r][4] = 'write';
        $this->rights[$r][5] = '';
        $r++;
        $this->rights[$r][0] = $this->numero . sprintf("%02d", $r + 1);
        $this->rights[$r][1] = 'Delete gtbcusage reports';
        $this->rights[$r][2] = 'd';
        $this->rights[$r][3] = 0;
        $this->rights[$r][4] = 'delete';
        $this->rights[$r][5] = '';
        $r++;

        // Main menu entries to add
        $this->menu = array();
        $r = 0;

        // Add here entries to declare new menus
        //
        // Example to declare a new Top Menu entry and its Left menu entry:
        $this->menu[$r++] = array(
            'fk_menu'=>'', // '' if this is a top menu. For left menu, use 'fk_mainmenu=xxx' or 'fk_mainmenu=xxx,fk_leftmenu=yyy' where xxx is mainmenucode and yyy is a leftmenucode
            'type'=>'top', // This is a Top menu entry
            'titre'=>'ModulegtbcusageName',
            'prefix' => img_picto('', $this->picto, 'class="paddingright pictofixedwidth valignmiddle"'),
            'mainmenu'=>'gtbcusage',
            'leftmenu'=>'',
            'url'=>'/custom/gtbcusage/Index.php',
            'langs'=>'gtbcusage@gtbcusage', // Lang file to use (without .lang) by module. File must be in langs/code_CODE/ directory.
            'position'=>1000 + $r,
            'enabled'=>'$conf->gtbcusage->enabled', // Define condition to show or hide menu entry. Use '$conf->gtbcusage->enabled' if entry must be visible if module is enabled.
            'perms'=>'$user->rights->gtbcusage->canuse', // Use 'perms'=>'$user->rights->gtbcusage->level1->level2' if you want your menu with a permission rules
            'target'=>'',
            'user'=>2, // 0=Menu for internal users, 1=external users, 2=both
        );

        $this->menu[$r++] = array(
            'fk_menu'=>'fk_mainmenu=gtbcusage', // '' if this is a top menu. For left menu, use 'fk_mainmenu=xxx' or 'fk_mainmenu=xxx,fk_leftmenu=yyy' where xxx is mainmenucode and yyy is a leftmenucode
            'type'=>'left', // This is a Left menu entry
            'titre'=>'Customer Aging',
            'mainmenu'=>'gtbcusage',
            'leftmenu'=>'customeraging',
            'url'=>'/custom/gtbcusage/Report/customer_aging.php',
            'langs'=>'gtbcusage@gtbcusage', // Lang file to use (without .lang) by module. File must be in langs/code_CODE/ directory.
            'position'=>1000 + $r,
            'enabled'=>'$conf->gtbcusage->enabled', // Define condition to show or hide menu entry. Use '$conf->gtbcusage->enabled' if entry must be visible if module is enabled. Use '$leftmenu==\'system\'' to show if leftmenu system is selected.
            'perms'=>'$user->rights->gtbcusage->canuse', // Use 'perms'=>'$user->rights->gtbcusage->level1->level2' if you want your menu with a permission rules
            'target'=>'',
            'user'=>2, // 0=Menu for internal users, 1=external users, 2=both
        );

        $this->menu[$r++] = array(
            'fk_menu'=>'fk_mainmenu=gtbcusage',
            'type'=>'left',
            'titre'=>'Customer Aging Search',
            'mainmenu'=>'gtbcusage',
            'leftmenu'=>'customeragingsearch',
            'url'=>'/custom/gtbcusage/Report/customer_aging_search.php',
            'langs'=>'gtbcusage@gtbcusage',
            'position'=>1000 + $r,
            'enabled'=>'$conf->gtbcusage->enabled',
            'perms'=>'$user->rights->gtbcusage->canuse',
            'target'=>'',
            'user'=>2,
        );

        $this->menu[$r++] = array(
            'fk_menu'=>'fk_mainmenu=gtbcusage',
            'type'=>'left',
            'titre'=>'Supplier Aging',
            'mainmenu'=>'gtbcusage',
            'leftmenu'=>'supplieraging',
            'url'=>'/custom/gtbcusage/Report/supplier_aging.php',
            'langs'=>'gtbcusage@gtbcusage',
            'position'=>1000 + $r,
            'enabled'=>'$conf->gtbcusage->enabled',
            'perms'=>'$user->rights->gtbcusage->canuse',
            'target'=>'',
            'user'=>2,
        );

        $this->menu[$r++] = array(
            'fk_menu'=>'fk_mainmenu=gtbcusage',
            'type'=>'left',
            'titre'=>'Supplier Aging Search',
            'mainmenu'=>'gtbcusage',
            'leftmenu'=>'supplieragingsearch',
            'url'=>'/custom/gtbcusage/Report/supplier_aging_search.php',
            'langs'=>'gtbcusage@gtbcusage',
            'position'=>1000 + $r,
            'enabled'=>'$conf->gtbcusage->enabled',
            'perms'=>'$user->rights->gtbcusage->canuse',
            'target'=>'',
            'user'=>2,
        );

        // Exports profiles provided by this module
        $r = 1;
        /* BEGIN MODULEBUILDER EXPORT gtbcusage */
        /*
        $langs->load("gtbcusage@gtbcusage");
        $this->export_code[$r]=$this->rights_class.'_'.$r;
        $this->export_label[$r]='gtbcusageLines';	// Translation key (used only if key ExportDataset_xxx_z not found)
        $this->export_icon[$r]='gtbcusage@gtbcusage';
        // Define $this->export_fields_array, $this->export_TypeFields_array and $this->export_entities_array
        $keyforclass = 'gtbcusage'; $keyforclassfile='/gtbcusage/class/gtbcusage.class.php'; $keyforelement='gtbcusage@gtbcusage';
        include DOL_DOCUMENT_ROOT.'/core/commonfieldsinexport.inc.php';
        //$this->export_fields_array[$r]['t.fieldtoadd']='FieldToAdd'; $this->export_TypeFields_array[$r]['t.fieldtoadd']='Text';
        //unset($this->export_fields_array[$r]['t.fieldtoremove']);
        //$keyforclass = 'gtbcusagedet'; $keyforclassfile='/gtbcusage/class/gtbcusage.class.php'; $keyforelement='gtbcusagedet@gtbcusage'; $keyforalias='td';
        //include DOL_DOCUMENT_ROOT.'/core/commonfieldsinexport.inc.php';
        $keyforselect='gtbcusage'; $keyforaliasextra='extra'; $keyforelement='gtbcusage@gtbcusage';
        include DOL_DOCUMENT_ROOT.'/core/extrafieldsinexport.inc.php';
        //$keyforselect='gtbcusagedet'; $keyforaliasextra='extradet'; $keyforelement='gtbcusagedet@gtbcusage';
        //include DOL_DOCUMENT_ROOT.'/core/extrafieldsinexport.inc.php';
        //$this->export_dependencies_array[$r] = array('gtbcusagedet'=>array('td.rowid','td.ref')); // To force to activate one or several fields if we select some fields that need same (like to select a unique key if we ask a field of a child to avoid the DISTINCT to discard them, or for computed field than need several other fields)
        //$this->export_special_array[$r] = array('t.field'=>'...');
        //$this->export_examplevalues_array[$r] = array('t.field'=>'Example');
        //$this->export_help_array[$r] = array('t.field'=>'FieldDescHelp');
        $this->export_sql_start[$r]='SELECT DISTINCT ';
        $this->export_sql_end[$r]  =' FROM '.MAIN_DB_PREFIX.'gtbcusage as t';
        //$this->export_sql_end[$r]  =' LEFT JOIN '.MAIN_DB_PREFIX.'gtbcusage_extrafields as extra ON t.rowid = extra.fk_object';
        //$this->export_sql_end[$r] .=' LEFT JOIN '.MAIN_DB_PREFIX.'gtbcusagedet as td ON t.rowid = td.fk_gtbcusage';
        //$this->export_sql_end[$r] .=' LEFT JOIN '.MAIN_DB_PREFIX.'gtbcusagedet_extrafields as extradet ON td.rowid = extradet.fk_object';
        $this->export_sql_end[$r] .=' WHERE 1 = 1';
        $this->export_sql_end[$r] .=' AND t.entity IN ('.getEntity('gtbcusage').')';
        $r++; */
        /* END MODULEBUILDER EXPORT gtbcusage */

        // Imports profiles provided by this module
        $r = 1;
        /* BEGIN MODULEBUILDER IMPORT gtbcusage */
        /*
        $langs->load("gtbcusage@gtbcusage");
        $this->import_code[$r]=$this->rights_class.'_'.$r;
        $this->import_label[$r]='gtbcusageLines';	// Translation key (used only if key ExportDataset_xxx_z not found)
        $this->import_icon[$r]='gtbcusage@gtbcusage';
        $this->import_tables_array[$r] = array('t' => MAIN_DB_PREFIX.'gtbcusage', 'extra' => MAIN_DB_PREFIX.'gtbcusage_extrafields');
        $this->import_tables_creator_array[$r] = array('t' => 'fk_user_author'); // Fields to store import user id
        $import_sample = array();
        $keyforclass = 'gtbcusage'; $keyforclassfile='/gtbcusage/class/gtbcusage.class.php'; $keyforelement='gtbcusage@gtbcusage';
        include DOL_DOCUMENT_ROOT.'/core/commonfieldsinimport.inc.php';
        $import_extrafield_sample = array();
        $keyforselect='gtbcusage'; $keyforaliasextra='extra'; $keyforelement='gtbcusage@gtbcusage';
        include DOL_DOCUMENT_ROOT.'/core/extrafieldsinimport.inc.php';
        $this->import_fieldshidden_array[$r] = array('extra.fk_object' => 'lastrowid-'.MAIN_DB_PREFIX.'gtbcusage');
        $this->import_regex_array[$r] = array();
        $this->import_examplevalues_array[$r] = array_merge($import_sample, $import_extrafield_sample);
        $this->import_updatekeys_array[$r] = array('t.ref' => 'Ref');
        $this->import_convertvalue_array[$r] = array(
            't.ref' => array(
                'rule'=>'getrefifauto',
                'class'=>'gtbcusage',
                'classfile'=>'/gtbcusage/class/gtbcusage.class.php',
                'classobject'=>'gtbcusage'
            )
        );
        $this->import_decodeentities_array[$r] = array('t.note_public' => 1, 't.note_private' => 1);
        $r++; */
        /* END MODULEBUILDER IMPORT gtbcusage */
    }

    /**
     *  Function called when module is enabled.
     *  The init function add constants, boxes, permissions and menus (defined in constructor) into Dolibarr database.
     *  It also creates data directories
     *
     *  @param      string  $options    Options when enabling module ('', 'noboxes')
     *  @return     int             	1 if OK, 0 if KO
     */
    public function init($options = '')
    {
        global $conf, $langs;

        //$result = $this->_load_tables('/install/mysql/', 'gtbcusage');
        $result = $this->_load_tables('/gtbcusage/sql/');
        if ($result < 0) {
            return -1; // Do not activate module if error 'not allowed' returned when loading module SQL queries (the _load_table run sql with run_sql with the error allowed parameter set to 'default')
        }

        // Create extrafields during init
        //include_once DOL_DOCUMENT_ROOT.'/core/class/extrafields.class.php';
        //$extrafields = new ExtraFields($this->db);
        //$result1=$extrafields->addExtraField('gtbcusage_myattr1', "New Attr 1 label", 'boolean', 1,  3, 'thirdparty',   0, 0, '', '', 1, '', 0, 0, '', '', 'gtbcusage@gtbcusage', '$conf->gtbcusage->enabled');
        //$result2=$extrafields->addExtraField('gtbcusage_myattr2', "New Attr 2 label", 'varchar', 1, 10, 'project',      0, 0, '', '', 1, '', 0, 0, '', '', 'gtbcusage@gtbcusage', '$conf->gtbcusage->enabled');
        //$result3=$extrafields->addExtraField('gtbcusage_myattr3', "New Attr 3 label", 'varchar', 1, 10, 'bank_account', 0, 0, '', '', 1, '', 0, 0, '', '', 'gtbcusage@gtbcusage', '$conf->gtbcusage->enabled');
        //$result4=$extrafields->addExtraField('gtbcusage_myattr4', "New Attr 4 label", 'select',  1,  3, 'thirdparty',   0, 1, '', array('options'=>array('code1'=>'Val1','code2'=>'Val2','code3'=>'Val3')), 1,'', 0, 0, '', '', 'gtbcusage@gtbcusage', '$conf->gtbcusage->enabled');
        //$result5=$extrafields->addExtraField('gtbcusage_myattr5', "New Attr 5 label", 'text',    1, 10, 'user',         0, 0, '', '', 1, '', 0, 0, '', '', 'gtbcusage@gtbcusage', '$conf->gtbcusage->enabled');

        // Permissions
        $this->remove($options);

        $sql = array();

        // Document templates
        $moduledir = dol_buildpath('/gtbcusage', 0);
        $myTmpObjects = array();
        $myTmpObjects['gtbcusage'] = array('includerefgeneration'=>0, 'includedocgeneration'=>0);

        foreach ($myTmpObjects as $myTmpObjectKey => $myTmpObjectArray) {
            if ($myTmpObjectArray['includerefgeneration']) {
                $src = DOL_DOCUMENT_ROOT.'/install/doctemplates/'.$myTmpObjectKey.'/template_gtbcusages.odt';
                $dirodt = DOL_DATA_ROOT.'/doctemplates/'.$myTmpObjectKey;
                $dest = $dirodt.'/template_gtbcusages.odt';

                if (file_exists($src) && !file_exists($dest)) {
                    require_once DOL_DOCUMENT_ROOT.'/core/lib/files.lib.php';
                    dol_mkdir($dirodt);
                    $result = dol_copy($src, $dest, 0, 0);
                    if ($result < 0) {
                        $langs->load("errors");
                        $this->error = $langs->trans('ErrorFailToCopyFile', $src, $dest);
                        return 0;
                    }
                }

                $sql = array_merge($sql, array(
                    "DELETE FROM ".MAIN_DB_PREFIX."document_model WHERE nom = 'standard_".strtolower($myTmpObjectKey)."' AND type = '".strtolower($myTmpObjectKey)."' AND entity = ".$conf->entity,
                    "INSERT INTO ".MAIN_DB_PREFIX."document_model (nom, type, entity) VALUES('standard_".strtolower($myTmpObjectKey)."','".strtolower($myTmpObjectKey)."',".$conf->entity.")",
                    "DELETE FROM ".MAIN_DB_PREFIX."document_model WHERE nom = 'generic_".strtolower($myTmpObjectKey)."_odt' AND type = '".strtolower($myTmpObjectKey)."' AND entity = ".$conf->entity,
                    "INSERT INTO ".MAIN_DB_PREFIX."document_model (nom, type, entity) VALUES('generic_".strtolower($myTmpObjectKey)."_odt', '".strtolower($myTmpObjectKey)."', ".$conf->entity.")"
                ));
            }
        }

        return $this->_init($sql, $options);
    }

    /**
     *  Function called when module is disabled.
     *  Remove from database constants, boxes and permissions from Dolibarr database.
     *  Data directories are not deleted
     *
     *  @param      string	$options    Options when enabling module ('', 'noboxes')
     *  @return     int                 1 if OK, 0 if KO
     */
    public function remove($options = '')
    {
        $sql = array();
        return $this->_remove($sql, $options);
    }
}
