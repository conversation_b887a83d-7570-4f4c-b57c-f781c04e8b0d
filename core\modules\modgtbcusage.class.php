<?php
include_once DOL_DOCUMENT_ROOT.'/core/modules/DolibarrModules.class.php';
//new
class modgtbcusage extends DolibarrModules
{
    public function __construct($db)
    {
        global $langs, $conf;

        $this->db = $db;
        $this->numero = 500111;
        $this->rights_class = 'gtbcusage';
        $this->family = "financial";
        $this->name = 'gtbcusage';
        $this->description = "Aging Reports for Customers and Suppliers";
        $this->descriptionlong = "This module provides aging reports to track outstanding balances for customers and suppliers.";
        $this->version = '6.09';
        $this->const_name = 'MAIN_MODULE_gtbcusage';
        $this->picto = 'bill';

        // Remove any blockchain or compliance references
        $this->module_parts = array();
        $this->always_enabled = false; // Make sure this is not forced to be enabled
        $this->core_enabled = false;   // This is not a core module

        // Define permissions
        $this->rights = array(
            0 => array(
                0 => '125035001',                    // Permission ID (unique number)
                1 => 'gtbcusage_CanUse',            // Permission label
                2 => 'r',                            // Permission type (r=read)
                3 => 0,                              // Permission default (0=off by default)
                4 => 'canuse',                       // Permission key
                5 => '',                             // Permission description
            ),
            1 => array(
                0 => '125035002',
                1 => 'gtbcusage_AddReport',
                2 => 'w',                            // w=write
                3 => 0,                              // Permission default (0=off by default)
                4 => 'addreport',
                5 => '',
            ),
            2 => array(
                0 => '125035003',
                1 => 'gtbcusage_EditReport',
                2 => 'w',
                3 => 0,
                4 => 'editreport',
                5 => '',
            ),
            3 => array(
                0 => '125035004',
                1 => 'gtbcusage_DeleteReport',
                2 => 'd',                            // d=delete
                3 => 0,
                4 => 'deletereport',
                5 => '',
            ),
            4 => array(
                0 => '125035005',
                1 => 'gtbcusage_ExportReport',
                2 => 'r',                            // r=read
                3 => 0,                              // Permission default (0=off by default)
                4 => 'exportreport',
                5 => '',
            )
        );

        // Menu entries
        $this->menu = array();
        $r = 0;

        // Add main menu entry for Aging Reports
        $this->menu[$r] = array(
            'fk_menu' => '',
            'type' => 'top',
            'titre' => 'AGING',
            'mainmenu' => 'agingreports',
            'url' => '/custom/gtbcusage/Index.php',
            'langs' => 'gtbcusage@gtbcusage',
            'position' => 100,
            'enabled' => '$conf->gtbcusage->enabled',
            'perms' => '$user->rights->gtbcusage->canuse',
            'target' => '',
            'user' => 2,
            'prefix' => '<span class="fas fa-calendar-alt pictofixedwidth valignmiddle"></span>'
        );
        $r++;

        // Customer Aging Report as submenu
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=agingreports',
            'type' => 'left',
            'titre' => 'Customer Age',
            'mainmenu' => 'agingreports',
            'leftmenu' => 'customeraging',
            'url' => '/custom/gtbcusage/Report/customer_aging.php',
            'langs' => 'gtbcusage@gtbcusage',
            'position' => 101,
            'enabled' => '$conf->gtbcusage->enabled',
            'perms' => '$user->rights->gtbcusage->canuse',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Customer Aging Branch Total Report as main left menu item
        // $this->menu[$r] = array(
        //     'fk_menu' => 'fk_mainmenu=agingreports',
        //     'type' => 'left',
        //     'titre' => 'Customer Age : Branch',
        //     'mainmenu' => 'agingreports',
        //     'leftmenu' => 'customeragingbranch',
        //     'url' => '/custom/gtbcusage/Report/customer_aging_branch_total.php',
        //     'langs' => '',
        //     'position' => 102,
        //     'enabled' => '1',
        //     'perms' => '1',
        //     'target' => '',
        //     'user' => 2,
        //     'picto' => 'object_age@gtbcusage'
        // );
        // $r++;

        // Customer Aging Search Report as main left menu item
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=agingreports',
            'type' => 'left',
            'titre' => 'Customer Age : Search',
            'mainmenu' => 'agingreports',
            'leftmenu' => 'customeragingsearch',
            'url' => '/custom/gtbcusage/Report/customer_aging_search.php',
            'langs' => 'gtbcusage@gtbcusage',
            'position' => 102,
            'enabled' => '$conf->gtbcusage->enabled',
            'perms' => '$user->rights->gtbcusage->canuse',
            'target' => '',
            'user' => 2
        );
        $r++;
        
        // Customer Statement Email Report
        // $this->menu[$r] = array(
        //     'fk_menu' => 'fk_mainmenu=agingreports',
        //     'type' => 'left',
        //     'titre' => 'Customer Statement : Email',
        //     'mainmenu' => 'agingreports',
        //     'leftmenu' => 'statementsemail',
        //     'url' => '/custom/gtbcusage/Report/staat.php',
        //     'langs' => '',
        //     'position' => 104,
        //     'enabled' => '1',
        //     'perms' => '1',
        //     'target' => '',
        //     'user' => 2,
        //     'picto' => 'object_age@gtbcusage'
        // );
        // $r++;

        // Supplier Aging Report as main left menu item
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=agingreports',
            'type' => 'left',
            'titre' => 'Supplier Age',
            'mainmenu' => 'agingreports',
            'leftmenu' => 'supplieraging',
            'url' => '/custom/gtbcusage/Report/supplier_aging.php',
            'langs' => 'gtbcusage@gtbcusage',
            'position' => 103,
            'enabled' => '$conf->gtbcusage->enabled',
            'perms' => '$user->rights->gtbcusage->canuse',
            'target' => '',
            'user' => 2
        );
        $r++;

        // Supplier Aging Search Report as main left menu item
        $this->menu[$r] = array(
            'fk_menu' => 'fk_mainmenu=agingreports',
            'type' => 'left',
            'titre' => 'Supplier Age : Search',
            'mainmenu' => 'agingreports',
            'leftmenu' => 'supplieragingsearch',
            'url' => '/custom/gtbcusage/Report/supplier_aging_search.php',
            'langs' => 'gtbcusage@gtbcusage',
            'position' => 104,
            'enabled' => '$conf->gtbcusage->enabled',
            'perms' => '$user->rights->gtbcusage->canuse',
            'target' => '',
            'user' => 2
        );
        $r++;
        
        // Customer Balance Detail Report as main left menu item
        // $this->menu[$r] = array(
        //     'fk_menu' => 'fk_mainmenu=agingreports',
        //     'type' => 'left',
        //     'titre' => 'Customer Balance : Search',
        //     'mainmenu' => 'agingreports',
        //     'leftmenu' => 'cusbaldet',
        //     'url' => '/custom/gtbcusage/Report/cusbaldet.php',
        //     'langs' => '',
        //     'position' => 107,
        //     'enabled' => '1',
        //     'perms' => '1',
        //     'target' => '',
        //     'user' => 2,
        //     'picto' => 'object_age@gtbcusage'
        // );
        // $r++;
    }

    public function init($options = '')
    {
        return $this->_init(array(), $options);
    }

    public function remove($options = '')
    {
        return $this->_remove(array(), $options);
    }
}
